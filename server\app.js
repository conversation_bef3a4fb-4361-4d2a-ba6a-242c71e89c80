require('dotenv').config();
const express = require('express');
const userApi = require('./api/user');
const personaApi = require('./api/persona');
const aiApi = require('./api/ai');
const chatApi = require('./api/chat');
const archiveApi = require('./api/archive');
const syncApi = require('./api/sync');
const aiConfigApi = require('./api/config');
const app = express();
const cors = require('cors');

app.use(cors());


// CSP中间件，允许base64图片和本地资源
app.use((req, res, next) => {
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; img-src 'self' data:; script-src 'self'; style-src 'self' 'unsafe-inline';"
  );
  next();
});

app.use(express.json());
app.use('/api/user', userApi);
app.use('/api/persona', personaApi);
app.use('/api/ai', aiApi);
app.use('/api/chat', chatApi);
app.use('/api/archive', archiveApi);
app.use('/api/sync', syncApi);
app.use('/api/configs/ai', aiConfigApi);

app.listen(3001, () => {
  console.log('Server running on http://localhost:3001');
});