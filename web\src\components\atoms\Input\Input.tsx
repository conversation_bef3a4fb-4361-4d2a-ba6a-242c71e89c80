import React, { InputHTMLAttributes, ReactNode, TextareaHTMLAttributes } from 'react';
import styled, { DefaultTheme } from 'styled-components';
import { useTheme } from 'design-system/theme/ThemeProvider';

// Define base props common to both input and textarea
interface CommonProps {
  inputPrefix?: ReactNode;
  inputSuffix?: ReactNode;
  error?: boolean;
}

// Discriminated union for InputProps
type InputProps = CommonProps & (
  | ({ as?: 'input' } & InputHTMLAttributes<HTMLInputElement>)
  | ({ as: 'textarea' } & TextareaHTMLAttributes<HTMLTextAreaElement>)
);

interface InputWrapperProps {
  error: boolean;
  theme: DefaultTheme;
}

const InputWrapper = styled.div<InputWrapperProps>`
  display: flex;
  align-items: center;
  /* PRD要求：更细、颜色更浅的边框 */
  border: 1px solid #DCDCDC; /* PRD推荐 #DCDCDC */
  border-radius: ${(props) => props.theme.radius.sm}; // Apple风格 6px-8px
  padding: ${(props) => props.theme.spacing.md} ${(props) => props.theme.spacing.lg};
  background-color: ${(props) => props.theme.colors.surface};
  transition: all 0.15s ease-out; // Apple风格更快的过渡
  min-height: 44px; // Apple推荐的最小触摸目标

  ${(props) =>
    props.error &&
    `
    border-color: ${props.theme.colors.red};
    box-shadow: 0 0 0 3px ${props.theme.colors.red}20; // 20% 透明度
  `}

  /* PRD要求：Focus状态边框使用优化后的主蓝色 */
  &:focus-within {
    border-color: ${(props) => props.theme.colors.primary}; /* #007AFF */
    box-shadow: 0 0 0 3px ${(props) => props.theme.colors.primary}15; /* 更柔和的焦点阴影 */
  }

  &:hover:not(:focus-within) {
    border-color: ${(props) => props.theme.colors.border};
  }
`;

// StyledInput will receive all props from Input, including `as`
const StyledInput = styled.input<InputProps & { theme: DefaultTheme }>`
  flex-grow: 1;
  border: none;
  outline: none;
  background-color: transparent;
  color: ${(props) => props.theme.colors.text};
  font-size: ${(props) => props.theme.typography.body.fontSize};
  font-weight: ${(props) => props.theme.typography.body.fontWeight};
  line-height: ${(props) => props.theme.typography.body.lineHeight};
  font-family: ${(props) => props.theme.typography.body.fontFamily};
  padding: 0;

  &::placeholder {
    /* PRD要求：Placeholder文字颜色使用更次要的灰色 */
    color: ${(props) => props.theme.colors.textTertiary}; /* #A0A0A5 */
    font-weight: 400; // Placeholder文字使用Regular字重
  }

  ${(props) => props.as === 'textarea' && `
    resize: vertical;
    min-height: 60px;
    line-height: 1.5;
  `}

  /* 移除浏览器默认样式 */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &[type=number] {
    -moz-appearance: textfield;
  }
`;

interface PrefixSuffixContainerProps {
  theme: DefaultTheme;
}

const PrefixSuffixContainer = styled.div<PrefixSuffixContainerProps>`
  display: flex;
  align-items: center;
  color: ${(props) => props.theme.colors.textSecondary};
  padding: 0 ${(props) => props.theme.spacing.xs};
`;

export const Input = ({
  inputPrefix,
  inputSuffix,
  error = false,
  as = 'input',
  ...props
}: InputProps) => {
  const { theme } = useTheme();

  return (
    <InputWrapper error={error}>
      {inputPrefix && <PrefixSuffixContainer>{inputPrefix}</PrefixSuffixContainer>}
      <StyledInput as={as} {...props} />
      {inputSuffix && <PrefixSuffixContainer>{inputSuffix}</PrefixSuffixContainer>}
    </InputWrapper>
  );
}; 