// 测试聊天流程的脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testChatFlow() {
  console.log('🧪 开始测试聊天流程...\n');

  try {
    // 1. 创建一个测试人格
    console.log('👤 创建测试人格...');
    const personaData = {
      name: '测试人格',
      description: '这是一个测试人格',
      traits: ['友好', '聪明'],
      prompt: '你是一个友好聪明的助手'
    };
    
    // 注意：这里需要认证token，我们先跳过人格创建，假设已有人格
    console.log('⚠️  跳过人格创建（需要认证），假设人格ID为1');
    
    // 2. 创建新对话
    console.log('\n💬 创建新对话...');
    const chatResponse = await axios.post(`${BASE_URL}/api/chat`, {
      personas: [1] // 假设人格ID为1
    });
    console.log('✅ 创建对话成功:', chatResponse.data);
    const chatId = chatResponse.data.id;
    
    // 3. 发送用户消息
    console.log('\n📝 发送用户消息...');
    const messageResponse = await axios.post(`${BASE_URL}/api/chat/${chatId}/message`, {
      sender: 1, // 人格ID
      content: '你好，这是一条测试消息',
      personaPrompt: '你是一个友好聪明的助手',
      personaName: '测试人格',
      personaDescription: '这是一个测试人格',
      personaTags: ['友好', '聪明']
    });
    console.log('✅ 发送消息成功:', messageResponse.data);
    
    // 4. 获取对话详情
    console.log('\n📋 获取对话详情...');
    const chatDetailResponse = await axios.get(`${BASE_URL}/api/chat/${chatId}`);
    console.log('✅ 对话详情:', JSON.stringify(chatDetailResponse.data, null, 2));
    
    // 5. 测试成长档案API
    console.log('\n📊 测试成长档案API...');
    const archiveResponse = await axios.get(`${BASE_URL}/api/archive/chats`);
    console.log('✅ 成长档案对话记录:', archiveResponse.data.length, '条');
    
    const reportResponse = await axios.get(`${BASE_URL}/api/archive/report`);
    console.log('✅ 成长报告:', reportResponse.data);
    
    console.log('\n🎉 聊天流程测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testChatFlow();
