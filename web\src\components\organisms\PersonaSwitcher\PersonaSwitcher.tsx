import React from 'react';
import styled from 'styled-components';
import { Ty<PERSON><PERSON>, Button } from '../../atoms';

interface Persona {
  id: string;
  name: string;
  avatar?: string;
}

interface PersonaSwitcherProps {
  personas: Persona[];
  selected: Persona | null;
  onChange: (persona: Persona) => void;
}

const PersonaSwitcher: React.FC<PersonaSwitcherProps> = ({
  personas,
  selected,
  onChange,
}) => {
  return (
    <SwitcherContainer>
      <Typography variant="callout">選擇人格:</Typography>
      <PersonaList>
        {personas.map((p) => (
          <PersonaButton
            key={p.id}
            onClick={() => onChange(p)}
            isSelected={selected?.id === p.id}
            title={`切換到 ${p.name}`}
          >
            {p.avatar && <Avatar src={p.avatar} alt={p.name} />}
            <span>{p.name}</span>
          </PersonaButton>
        ))}
      </PersonaList>
    </SwitcherContainer>
  );
};

// Apple风格人格选择器 - PRD要求优化选择器样式，使其更具交互感
const SwitcherContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.lg};
  flex-wrap: wrap;

  /* 标签文字样式 */
  p {
    color: ${({ theme }) => theme.colors.textSecondary};
    font-weight: 500;
    margin: 0;
    white-space: nowrap;
  }
`;

const PersonaList = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  flex-wrap: wrap;
  align-items: center;
`;

// PRD要求：人格选择器样式优化，选中项和未选中项样式区分
const PersonaButton = styled.button<{ isSelected: boolean }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
  /* PRD要求：选中项背景色、文字色更新 */
  background-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.surface};
  color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.surface : theme.colors.text};
  /* PRD要求：未选中项样式优化 - 浅灰背景深灰字，或主蓝色边框+文字 */
  border: 1px solid ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.primary};
  border-radius: ${({ theme }) => theme.radius.full}; // 胶囊状
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};
  transition: all 0.15s ease-out; // Apple风格更快的过渡
  cursor: pointer;
  font-family: ${({ theme }) => theme.typography.callout.fontFamily};
  font-size: ${({ theme }) => theme.typography.callout.fontSize};
  font-weight: 500;
  min-height: 40px; // 确保足够的触摸目标
  box-shadow: ${({ theme, isSelected }) =>
    isSelected ? theme.shadows.sm : 'none'};

  /* 未选中项使用主蓝色文字 */
  ${({ theme, isSelected }) => !isSelected && `
    background-color: transparent;
    color: ${theme.colors.primary};
  `}

  &:hover:not(:disabled) {
    background-color: ${({ theme, isSelected }) =>
      isSelected ? theme.colors.primary : `${theme.colors.primary}10`};
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }

  &:active {
    transform: translateY(0px);
  }

  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }

  /* 文字样式 */
  span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
  }
`;

const Avatar = styled.img`
  width: 28px;
  height: 28px;
  border-radius: ${({ theme }) => theme.radius.round};
  object-fit: cover;
  flex-shrink: 0;
  border: 2px solid ${({ theme }) => theme.colors.surface};
  box-shadow: ${({ theme }) => theme.shadows.xs};
`;

export default PersonaSwitcher; 