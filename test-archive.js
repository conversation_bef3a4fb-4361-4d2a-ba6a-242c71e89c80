// 测试成长档案API的简单脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testArchiveAPI() {
  console.log('🧪 开始测试成长档案API...\n');

  try {
    // 测试获取对话记录
    console.log('📋 测试获取对话记录...');
    const chatsResponse = await axios.get(`${BASE_URL}/api/archive/chats`);
    console.log(`✅ 获取到 ${chatsResponse.data.length} 条对话记录`);
    
    // 测试获取成长报告
    console.log('\n📊 测试获取成长报告...');
    const reportResponse = await axios.get(`${BASE_URL}/api/archive/report`);
    console.log('✅ 成长报告数据:', reportResponse.data);
    
    // 测试获取情绪分析
    console.log('\n😊 测试获取情绪分析...');
    const emotionResponse = await axios.get(`${BASE_URL}/api/archive/emotion`);
    console.log('✅ 情绪分析数据:', emotionResponse.data);
    
    // 测试获取高光记录
    console.log('\n⭐ 测试获取高光记录...');
    const highlightsResponse = await axios.get(`${BASE_URL}/api/archive/highlights`);
    console.log(`✅ 获取到 ${highlightsResponse.data.length} 条高光记录`);
    
    console.log('\n🎉 所有API测试通过！');
    
  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testArchiveAPI();
