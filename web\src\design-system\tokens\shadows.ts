// Apple风格阴影系统 - PRD V1.1优化的柔和、弥散阴影
export const shadows = {
  none: 'none',
  // 非常轻微的阴影，用于悬浮状态
  xs: '0px 1px 3px rgba(0, 0, 0, 0.04)',
  // 标准卡片阴影 - PRD推荐: Y轴偏移略大，X轴偏移小，模糊半径增大
  sm: '0px 4px 12px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04)',
  // 中等阴影，用于悬浮卡片 - 更柔和弥散
  md: '0px 6px 16px rgba(0, 0, 0, 0.08), 0px 3px 8px rgba(0, 0, 0, 0.06)',
  // 较强阴影，用于模态框 - 避免纯黑色，使用带微弱色调的深灰
  lg: '0px 12px 32px rgba(0, 0, 0, 0.10), 0px 4px 12px rgba(0, 0, 0, 0.08)',
  // 最强阴影，用于弹出层
  xl: '0px 20px 48px rgba(0, 0, 0, 0.12), 0px 8px 20px rgba(0, 0, 0, 0.10)',
  // 特大阴影，用于全屏覆盖
  xxl: '0px 28px 64px rgba(0, 0, 0, 0.14), 0px 12px 28px rgba(0, 0, 0, 0.12)',
};

export type Shadows = typeof shadows; 