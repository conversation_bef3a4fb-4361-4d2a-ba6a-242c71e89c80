// Apple风格颜色系统 - 根据PRD V1.1要求优化
export const lightColors = {
  // 主要强调色 - Apple蓝 (PRD推荐: #007AFF)
  primary: '#007AFF',
  secondary: '#5856D6', // 紫色作为次要强调色
  tertiary: '#32D74B', // 绿色
  quaternary: '#FF9500', // 橙色

  // 背景色系统 - PRD要求侧边栏与内容区明确区分
  background: '#F5F5F7', // 主背景色/侧边栏背景 - PRD推荐
  surface: '#FFFFFF', // 卡片/内容区背景色 - 纯白
  surfaceSecondary: '#F2F2F7', // 次要表面色

  // 文字色系统 - PRD优化的灰阶层次
  text: '#1D1D1F', // 主要文字色 - PRD推荐 #1D1D1F 或 #333333
  textSecondary: '#6E6E73', // 次要文字色 - PRD推荐 #555555 或 #6E6E73
  textTertiary: '#A0A0A5', // 更次要文字/Placeholder色 - PRD推荐 #888888 或 #A0A0A5

  // 边框和分割线 - PRD优化的边框色
  separator: '#DCDCDC', // 分割线颜色 - PRD推荐更浅的灰色
  border: '#E0E0E0', // 边框色 - PRD推荐 #DCDCDC 或 #E0E0E0

  // 状态色系统 - PRD优化的状态色
  red: '#FF3B30', // 错误/禁用 - PRD推荐 #FF3B30
  green: '#34C759', // 成功/启用 - PRD推荐 #34C759
  blue: '#007AFF', // 信息
  yellow: '#FF9500', // 警告 - PRD推荐 #FF9500 (橙色)
  orange: '#FF9500', // 警告/待定
  purple: '#5856D6', // 装饰色
  pink: '#FF2D92', // 装饰色
  teal: '#5AC8FA', // 装饰色
  indigo: '#5856D6', // 装饰色
  brown: '#A2845E', // 装饰色
  gray: '#8E8E93', // 中性灰
};

export const darkColors = {
  // 主要强调色 - 在深色模式下稍微调亮
  primary: '#0A84FF',
  secondary: '#5E5CE6',
  tertiary: '#30D158',
  quaternary: '#FF9F0A',

  // 背景色系统
  background: '#000000', // 主背景色 - 纯黑
  surface: '#1C1C1E', // 卡片/内容区背景色 - 深灰
  surfaceSecondary: '#2C2C2E', // 次要表面色

  // 文字色系统
  text: '#FFFFFF', // 主要文字色 - 纯白
  textSecondary: '#EBEBF5', // 次要文字色，透明度60%
  textTertiary: '#EBEBF5', // 三级文字色，透明度30%

  // 边框和分割线
  separator: '#38383A', // 分割线颜色
  border: '#48484A', // 边框颜色

  // 状态色系统
  red: '#FF453A',
  green: '#32D74B',
  blue: '#0A84FF',
  yellow: '#FFD60A',
  orange: '#FF9F0A',
  purple: '#5E5CE6',
  pink: '#FF375F',
  teal: '#64D2FF',
  indigo: '#5E5CE6',
  brown: '#AC8E68',
  gray: '#8E8E93',
};



export type Colors = typeof lightColors; 