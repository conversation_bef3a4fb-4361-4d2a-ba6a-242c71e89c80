import React, { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { AuthLayout } from '../../layouts/AuthLayout';
import { Button, Input, Typography } from '../../components/atoms';
import { Card, FormField } from '../../components/molecules';
import { LoadingSpinner } from '../../components/animations/LoadingSpinner';
import { useAuth } from '../../contexts/AuthContext';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../design-system/theme/ThemeProvider';

// Apple风格登录页面样式
const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${(props) => props.theme.spacing.xl}; // 增加间距，创造呼吸感
  margin-top: ${(props) => props.theme.spacing.xxl};
`;

// PRD要求：标题加大加粗
const PageTitle = styled(Typography)`
  text-align: center;
  margin-bottom: ${(props) => props.theme.spacing.lg};
  color: ${(props) => props.theme.colors.text};
`;

const ErrorMessage = styled(Typography)`
  margin-top: ${(props) => props.theme.spacing.lg};
  text-align: center;
  padding: ${(props) => props.theme.spacing.md} ${(props) => props.theme.spacing.lg};
  background-color: ${(props) => props.theme.colors.red}10; // 10% 透明度背景
  border-radius: ${(props) => props.theme.radius.sm};
  border-left: 3px solid ${(props) => props.theme.colors.red};
`;

const RegisterSection = styled.div`
  margin-top: ${(props) => props.theme.spacing.xxl};
  text-align: center;
  padding-top: ${(props) => props.theme.spacing.xl};
  border-top: 1px solid ${(props) => props.theme.colors.separator};
`;

const RegisterText = styled(Typography)`
  margin-bottom: ${(props) => props.theme.spacing.lg};
  color: ${(props) => props.theme.colors.textSecondary};
`;

// 增加品牌Logo区域（可选）
const BrandSection = styled.div`
  text-align: center;
  margin-bottom: ${(props) => props.theme.spacing.xxl};
`;

const BrandTitle = styled(Typography)`
  color: ${(props) => props.theme.colors.primary}; /* PRD要求：使用优化后的主蓝色 #007AFF */
  margin-bottom: ${(props) => props.theme.spacing.sm};
  font-weight: 600; /* PRD要求：微调Logo样式，增加字重 */
`;

const BrandSubtitle = styled(Typography)`
  color: ${(props) => props.theme.colors.textSecondary};
`;

const Login: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const { login } = useAuth();
  const [account, setAccount] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    try {
      const res = await axios.post('/api/user/login', { account, password });
      await login((res.data as { token: string }).token);
      navigate('/');
    } catch (err: any) {
      setError(err.response?.data?.message || '登錄失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <Card
        as={motion.div}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        {/* PRD要求：可选的品牌Logo */}
        <BrandSection>
          <BrandTitle variant="title2">世另我</BrandTitle>
          <BrandSubtitle variant="caption1">與不同的自己對話</BrandSubtitle>
        </BrandSection>

        {/* PRD要求：标题加大加粗 */}
        <PageTitle variant="largeTitle">歡迎回來</PageTitle>

        <Form onSubmit={handleSubmit}>
          <FormField label="賬號">
            <Input
              type="text"
              placeholder="請輸入賬號"
              value={account}
              onChange={(e) => setAccount(e.target.value)}
              required
            />
          </FormField>
          <FormField label="密碼">
            <Input
              type="password"
              placeholder="請輸入密碼"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </FormField>
          {/* PRD要求：主要操作按钮使用主要按钮样式 */}
          <Button
            variant="primary"
            size="lg"
            loading={loading}
            disabled={loading}
            type="submit"
          >
            {loading ? <LoadingSpinner /> : '登錄'}
          </Button>
        </Form>

        {error && (
          <ErrorMessage variant="callout" color={theme.colors.red}>
            {error}
          </ErrorMessage>
        )}

        <RegisterSection>
          <RegisterText variant="callout">
            還沒有帳號？
          </RegisterText>
          {/* PRD要求：次要链接使用文本链接样式 */}
          <Button
            variant="text"
            size="md"
            onClick={() => navigate('/auth/register')}
            disabled={loading}
          >
            立即註冊
          </Button>
        </RegisterSection>
      </Card>
    </AuthLayout>
  );
};

export default Login; 