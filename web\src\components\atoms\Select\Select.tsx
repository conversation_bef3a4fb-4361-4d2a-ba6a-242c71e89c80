import React from 'react';
import styled from 'styled-components';

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  options: SelectOption[];
  value: string;
  onChange: (event: React.ChangeEvent<HTMLSelectElement>) => void;
}

const Select: React.FC<SelectProps> = ({
  options,
  value,
  onChange,
  ...props
}) => {
  return (
    <StyledSelect value={value} onChange={onChange} {...props}>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </StyledSelect>
  );
};

const StyledSelect = styled.select`
  /* PRD要求：样式与输入框统一 */
  padding: ${({ theme }) => theme.spacing.md} ${({ theme }) => theme.spacing.lg};
  /* PRD要求：更细、颜色更浅的边框 */
  border: 1px solid #DCDCDC; /* PRD推荐 #DCDCDC */
  border-radius: ${({ theme }) => theme.radius.sm}; /* Apple风格 6px-8px */
  background-color: ${({ theme }) => theme.colors.surface};
  color: ${({ theme }) => theme.colors.text};
  font-family: ${({ theme }) => theme.typography.body.fontFamily};
  font-size: ${({ theme }) => theme.typography.body.fontSize};
  line-height: ${({ theme }) => theme.typography.body.lineHeight};
  cursor: pointer;
  transition: all 0.15s ease-out; /* Apple风格更快的过渡 */
  min-height: 44px; /* Apple推荐的最小触摸目标 */

  /* PRD要求：Focus状态边框使用优化后的主蓝色 */
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary}; /* #007AFF */
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary}15; /* 更柔和的焦点阴影 */
  }

  &:hover:not(:focus) {
    border-color: ${({ theme }) => theme.colors.border};
  }
`;

export { Select }; 