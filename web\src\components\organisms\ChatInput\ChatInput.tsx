import React, { useState } from 'react';
import styled from 'styled-components';
import { Input, Button, Typography, Icon } from '../../atoms';

interface ChatInputProps {
  onSend: (content: string) => void;
  disabled: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({ onSend, disabled }) => {
  const [message, setMessage] = useState('');

  const handleSend = () => {
    if (message.trim()) {
      onSend(message);
      setMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <InputContainer>
      <TextAreaWrapper>
        <TextArea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="輸入訊息..."
          disabled={disabled}
          rows={1}
        />
      </TextAreaWrapper>
      <SendButton
        onClick={handleSend}
        disabled={disabled || !message.trim()}
        title={disabled ? "正在處理..." : "發送訊息"}
      >
        <Icon name="send" />
      </SendButton>
    </InputContainer>
  );
};

// Apple风格聊天输入组件 - PRD要求优化输入框和发送按钮样式
const InputContainer = styled.div`
  display: flex;
  align-items: flex-end;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.xl} ${({ theme }) => theme.spacing.pageSpacing};
  border-top: 1px solid ${({ theme }) => theme.colors.separator};
  background-color: ${({ theme }) => theme.colors.background};
  backdrop-filter: blur(20px); // Apple风格毛玻璃效果
  -webkit-backdrop-filter: blur(20px);
`;

const TextAreaWrapper = styled.div`
  flex-grow: 1;
  position: relative;
`;

const TextArea = styled.textarea`
  width: 100%;
  resize: none;
  min-height: 44px; // Apple推荐的最小触摸目标
  max-height: 120px;
  padding: ${({ theme }) => theme.spacing.md} ${({ theme }) => theme.spacing.lg};
  border-radius: ${({ theme }) => theme.radius.xl}; // 更圆润的输入框
  /* PRD要求：更细、颜色更浅的边框 */
  border: 1px solid #DCDCDC; /* PRD推荐 #DCDCDC */
  background-color: ${({ theme }) => theme.colors.surface};
  color: ${({ theme }) => theme.colors.text};
  font-family: ${({ theme }) => theme.typography.body.fontFamily};
  font-size: ${({ theme }) => theme.typography.body.fontSize};
  line-height: ${({ theme }) => theme.typography.body.lineHeight};
  transition: all 0.15s ease-out;
  outline: none;

  &::placeholder {
    /* PRD要求：Placeholder文字颜色使用更次要的灰色 */
    color: ${({ theme }) => theme.colors.textTertiary}; /* #A0A0A5 */
  }

  /* PRD要求：Focus状态边框使用优化后的主蓝色 */
  &:focus {
    border-color: ${({ theme }) => theme.colors.primary}; /* #007AFF */
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary}15; /* 更柔和的焦点阴影 */
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: ${({ theme }) => theme.colors.separator};
    border-radius: ${({ theme }) => theme.radius.round};
  }
`;

// PRD要求：发送按钮样式优化
const SendButton = styled.button<{ disabled: boolean }>`
  height: 44px;
  width: 44px;
  min-width: 44px;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: ${({ theme }) => theme.radius.round}; // 完全圆形
  border: none;
  background-color: ${({ theme, disabled }) =>
    disabled ? theme.colors.separator : theme.colors.primary};
  color: ${({ theme }) => theme.colors.surface};
  cursor: ${({ disabled }) => disabled ? 'not-allowed' : 'pointer'};
  transition: all 0.15s ease-out;
  box-shadow: ${({ theme, disabled }) =>
    disabled ? 'none' : theme.shadows.sm};

  &:hover:not(:disabled) {
    background-color: ${({ theme }) => theme.colors.primary};
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.md};
  }

  &:active:not(:disabled) {
    transform: translateY(0px);
    opacity: 0.8;
  }

  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }

  svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
  }
`;

export default ChatInput; 