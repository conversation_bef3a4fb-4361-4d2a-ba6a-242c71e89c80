const express = require('express');
const router = express.Router();
const chatStorage = require('../models/chatStorage');

// 高光記錄：{ chatId, messageIdx }
let highlights = [];

// 查詢所有對話記錄
router.get('/chats', (req, res) => {
  const chats = chatStorage.getAllChats();
  res.json(chats);
});

// 標記/取消高光
router.post('/highlight', (req, res) => {
  const { chatId, messageIdx } = req.body;
  if (typeof chatId !== 'number' || typeof messageIdx !== 'number') return res.status(400).json({ message: '參數錯誤' });
  const idx = highlights.findIndex(h => h.chatId === chatId && h.messageIdx === messageIdx);
  if (idx === -1) {
    highlights.push({ chatId, messageIdx });
    res.json({ message: '已標記高光' });
  } else {
    highlights.splice(idx, 1);
    res.json({ message: '已取消高光' });
  }
});

// 查詢高光
router.get('/highlights', (req, res) => {
  res.json(highlights);
});

// 生成成長報告（統計消息數、對話數、活跃人格、成长指数、每日活动数据）
router.get('/report', (req, res) => {
  const chats = chatStorage.getAllChats();
  const totalConversations = chats.length;
  const totalMessages = chats.reduce((sum, c) => sum + c.messages.length, 0);

  // 计算活跃人格数量（参与过对话的不同人格）
  const activePersonasSet = new Set();
  chats.forEach(chat => {
    chat.personas.forEach(personaId => {
      activePersonasSet.add(personaId);
    });
  });
  const activePersonas = activePersonasSet.size;

  // 计算成长指数（基于对话数量、消息数量和活跃度的综合评分）
  const growthIndex = Math.min(100, Math.round(
    (totalConversations * 10 + totalMessages * 2 + activePersonas * 15) / 10
  ));

  // 生成每日活动数据（最近7天的对话活动）
  const dailyActivity = [];
  const today = new Date();

  for (let i = 6; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateStr = date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });

    // 计算该日期的对话数量
    const dayStart = new Date(date);
    dayStart.setHours(0, 0, 0, 0);
    const dayEnd = new Date(date);
    dayEnd.setHours(23, 59, 59, 999);

    const dayMessages = chats.reduce((count, chat) => {
      return count + chat.messages.filter(msg => {
        const msgDate = new Date(msg.timestamp);
        return msgDate >= dayStart && msgDate <= dayEnd;
      }).length;
    }, 0);

    dailyActivity.push({
      name: dateStr,
      value: dayMessages
    });
  }

  res.json({
    totalConversations,
    activePersonas,
    growthIndex,
    dailyActivity
  });
});

// 簡單情緒分析（正負面詞彙統計）
const positiveWords = ['好', '棒', '開心', '快樂', '讚'];
const negativeWords = ['難過', '生氣', '糟', '壞', '失望'];
router.get('/emotion', (req, res) => {
  const chats = chatStorage.getAllChats();
  let pos = 0, neg = 0;
  chats.forEach(c => c.messages.forEach(m => {
    positiveWords.forEach(w => { if (m.content.includes(w)) pos++; });
    negativeWords.forEach(w => { if (m.content.includes(w)) neg++; });
  }));
  res.json({ positive: pos, negative: neg });
});

module.exports = router; 