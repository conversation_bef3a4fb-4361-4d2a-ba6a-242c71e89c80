**PRD (产品需求文档): 系统 UI/UX 优化 (Apple 风格) - V1.1 迭代**

**1. 文档信息**

*   **项目名称:** [你的项目名称] - 系统 UI/UX 优化
*   **版本号:** 1.1 (基于 V1.0 的迭代优化)
*   **创建日期:** [YYYY-MM-DD]
*   **最后更新日期:** [YYYY-MM-DD]
*   **创建人:** [你的名字/团队]
*   **负责人:** [项目负责人]
*   **相关人员:** [设计师]、[前端开发工程师]、[产品经理]
*   **参考V1.0 PRD:** [[链接到V1.0 PRD文档](prd20250624.md)]

**2. 项目背景与目标**

*   **2.1 项目背景:**
    *   V1.0 版本已根据 Apple 风格对系统 UI 进行了初步优化，整体观感得到显著提升，卡片式布局、圆角、阴影及部分组件已基本成型。
    *   当前版本在色彩细腻度、字体排版、图标应用、组件细节及交互一致性方面仍有进一步打磨和提升的空间，以更贴近精致、专业的 Apple 设计语言。
*   **2.2 项目目标 (V1.1):**
    *   **极致视觉优化:** 微调色彩方案、字体层级及间距，使界面更细腻、更具质感。
    *   **增强品牌感与专业度:** 引入风格统一的图标，完善组件细节，提升整体视觉一致性。
    *   **提升用户体验细节:** 优化操作按钮的视觉区分，改善标签和状态提示的可读性。
    *   **完善设计规范:** 固化优化后的设计元素，形成更完善的 UI Kit / 设计规范。

**3. 现有版本 (V1.0) 评估与 V1.1 优化方向**

*   **3.1 已达成:**
    *   整体布局清爽，卡片化设计初步建立。
    *   圆角和基础阴影已应用。
    *   侧边栏用户信息区域结构清晰。
    *   登录/注册页品牌感有所体现。
*   **3.2 V1.1 重点优化方向:**
    *   **色彩体系细化:** 主强调色（蓝色）及中性色（灰阶）的精确选择与应用。
    *   **排版与字体优化:** 字体家族选择，字重、字号层级细化，行高调整。
    *   **图标系统引入:** 为关键导航和操作元素配备图标。
    *   **组件细节打磨:** 按钮、标签、输入框、卡片阴影等样式的进一步精致化。
    *   **间距与留白优化:** 进一步营造视觉呼吸感。
    *   **状态与反馈增强:** 优化选中、禁用、启用等状态的视觉表现。

**4. 需求详述 - 整体设计规范优化 (Apple 风格 V1.1)**

*   **4.1 色彩方案微调:**
    *   **UI-COLOR-001: 主强调色 (蓝色) 优化:**
        *   当前蓝色饱和度较高，调整为更沉稳、略微去饱和或稍深的蓝色 (参考: `#007AFF`)。
        *   应用于：侧边栏选中项背景、主要按钮、选中的标签、输入框Focus状态边框等。
    *   **UI-COLOR-002: 中性色 (灰阶) 层次优化:**
        *   主要文字色: `#1D1D1F` 或 `#333333`。
        *   次要文字色: `#555555` 或 `#6E6E73`。
        *   更次要文字/Placeholder色: `#888888` 或 `#A0A0A5`。
        *   边框色: 非常浅的灰色 (如 `#DCDCDC` 或 `#E0E0E0`)。
        *   侧边栏背景: 与内容区背景形成明确区分，如内容区为纯白，侧边栏可为 `#F5F5F7`。
    *   **UI-COLOR-003: 卡片阴影优化:**
        *   调整阴影参数，使其更柔和、弥散。Y轴偏移可略大，X轴偏移小，模糊半径增大 (参考: `box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.04);`)。
        *   避免纯黑色阴影，考虑带微弱色调的深灰阴影。
    *   **UI-COLOR-004: 状态色优化:**
        *   成功/启用 (绿色): 参考 `#34C759`。
        *   错误/禁用 (红色): 参考 `#FF3B30`。
        *   警告 (黄色/橙色): 参考 `#FF9500`。
        *   上述颜色可根据实际搭配调整其浅色背景版。

*   **4.2 字体与排版优化:**
    *   **UI-TYPO-001: 字体家族确认:**
        *   首选: `SF Pro Text` / `SF Pro Display` (或其Web Font变体)。
        *   备选: `Inter` (确保中文字体与之协调)。
    *   **UI-TYPO-002: 字重 (Font Weight) 应用细化:**
        *   页面大标题: Semi-bold (600) 或 Bold (700)。
        *   卡片/区域标题: Medium (500) 或 Semi-bold (600)。
        *   正文/描述性文字: Regular (400)。
        *   按钮文字: Medium (500)。
    *   **UI-TYPO-003: 行高 (Line Height) 调整:**
        *   描述性文本行高增加至 `1.6` - `1.7`。
    *   **UI-TYPO-004: 字号层级进一步区分:** 确保不同信息层级的字号有明显差异。

*   **4.3 图标系统引入与应用:**
    *   **UI-ICON-001: 侧边栏菜单图标:**
        *   为所有侧边栏一级菜单项（对话、成长档案、管理员、大模型配置等）配备风格统一的简洁线条图标 (参考 SF Symbols, Heroicons, Lucide)。
    *   **UI-ICON-002: 按钮内嵌图标 (可选):**
        *   为部分操作按钮（如“创建新人格”前置 `+` 图标，“編輯”前置笔形图标）增加图标，提升识别性。图标与文字间距适中。
    *   **UI-ICON-003: 状态提示图标:**
        *   考虑在“禁用”/“啟用”等状态标签前增加小的圆形指示图标。
    *   **UI-ICON-004: 用户头像图标化:** 侧边栏用户信息区域的数字头像背景色使用优化后的主蓝色，数字用白色。
    *   **UI-ICON-005: 主题切换图标:** 侧边栏底部圆形按钮如为主题切换，使用明确的太阳/月亮图标。

*   **4.4 间距与留白优化:**
    *   **UI-SPACE-001: 组件内部间距微调:** 审视卡片内各元素（标题、描述、标签、按钮组）的垂直间距，确保呼吸感。
    *   **UI-SPACE-002: 按钮组间距:** 按钮组内各按钮（如“編輯”、“刪除”、“切換”）之间的水平间距适当拉开。
    *   **UI-SPACE-003: 页面级间距审视:** 内容区与侧边栏、顶部全局元素与主要内容区之间的间距。

*   **4.5 基础组件样式打磨:**
    *   **UI-COMP-BTN-001: 按钮视觉层级区分:**
        *   **主要操作按钮 (如“切換”、“創建新人格”):** 使用优化后的主蓝色背景。
        *   **次要操作按钮 (如“編輯”、“刪除”):**
            *   选项1: 浅灰色背景 (`#E8E8ED` 或类似)，深灰色文字。
            *   选项2: 透明背景，优化后的主蓝色文字 (文本按钮)。
            *   选项3: 透明背景，优化后的主蓝色细边框，优化后的主蓝色文字 (幽灵按钮)。
    *   **UI-COMP-TAG-001: 标签/徽章样式优化:**
        *   背景色: 更柔和（如主蓝色的极浅色版、浅灰色、或根据状态色调整的浅色背景）。
        *   文字颜色: 与背景形成清晰对比。
        *   内边距(Padding): 适当增加，避免文字紧贴边缘。
        *   圆角: 可略小，不完全是胶囊状，或保持一致的小圆角矩形。
    *   **UI-COMP-INPUT-001: 输入框/选择器样式优化:**
        *   边框: 更细、颜色更浅 (如 `#DCDCDC`)。
        *   Focus状态: 边框使用优化后的主蓝色。
        *   Placeholder文字颜色使用更次要的灰色。
    *   **UI-COMP-CARD-001: 卡片阴影:** 应用优化后的阴影参数 (见 UI-COLOR-003)。

**5. 具体页面/模块优化需求 (V1.1 迭代)**

*   **5.1 登录/注册页 (图5, 图6):**
    *   **UI-LOGIN-101:** “世另我”Logo及标语字体、颜色、间距微调，追求更佳视觉效果。
    *   **UI-LOGIN-102:** 输入框边框、Focus状态颜色更新。
    *   **UI-LOGIN-103:** “登錄”、“註冊”按钮颜色更新为主蓝色优化版。

*   **5.2 侧边栏导航 (全局):**
    *   **UI-NAV-101:** 选中项背景色更新为主蓝色优化版。
    *   **UI-NAV-102:** 为所有一级菜单项添加图标 (UI-ICON-001)。
    *   **UI-NAV-103:** 用户信息区域头像圆形背景色更新为主蓝色优化版，数字白色。
    *   **UI-NAV-104:** 底部主题切换按钮使用明确图标 (UI-ICON-005)。

*   **5.3 人格管理页 (图1):**
    *   **UI-PM-101:** 页面标题“人格管理”字号、字重调整 (UI-TYPO-002)。
    *   **UI-PM-102:** “創建新人格”按钮颜色更新。
    *   **UI-PM-103:** 卡片内：
        *   描述性文字行高调整 (UI-TYPO-003)。
        *   目标描述标签样式优化 (UI-COMP-TAG-001)。
        *   “編輯”、“刪除”按钮样式调整为次要操作样式 (UI-COMP-BTN-001)。
        *   “切換”按钮颜色更新。
        *   按钮间距调整 (UI-SPACE-002)。

*   **5.4 對話页 (图2):**
    *   **UI-CHAT-101:** “選擇人格”标签组：
        *   选中项背景色、文字色更新。
        *   未选中项样式优化 (如浅灰背景深灰字，或主蓝色边框+文字)。
    *   **UI-CHAT-102:** 聊天输入框边框、Focus状态优化。
    *   **UI-CHAT-103:** 右下角发送按钮内图标优化，背景色更新。
    *   **UI-CHAT-104:** （若V1.0未实现）设计并实现聊天区空状态提示。

*   **5.5 成長檔案页 (图3):**
    *   **UI-ARCHIVE-101:** 统计卡片内：
        *   核心数字（如 `+12%`）字号、字重、颜色进一步突出。
        *   辅助描述文字样式调整。
        *   增长率绿色调整为优化后的状态绿色 (UI-COLOR-004)。
    *   **UI-ARCHIVE-102:** “搜尋對話記錄”输入框和“近一週”选择器样式统一并优化。
    *   **UI-ARCHIVE-103:** 图表区若有实际数据，确保图表配色与优化后的色彩体系协调。

*   **5.6 AI 配置管理页 (图4):**
    *   **UI-AICFG-101:** “批量操作”按钮调整为次要操作样式。
    *   **UI-AICFG-102:** “添加新配置”按钮颜色更新。
    *   **UI-AICFG-103:** 卡片内：
        *   模型名称等主要信息字重、颜色调整。
        *   API URL等次要信息颜色调浅。
        *   “禁用”/“啟用”标签样式优化，使用优化后的状态色 (UI-COLOR-004)，可考虑加入状态指示小图标 (UI-ICON-003)。
        *   “編輯”、“刪除”按钮调整为次要操作样式。

**6. 技术实现方案调整 (V1.1)**

*   **6.1 CSS 方案:** 继续沿用V1.0选定的方案 (Styled-components / Tailwind CSS)。
    *   **重点:** 更新 `theme.ts` (Styled-components) 或 `tailwind.config.js` (Tailwind CSS) 中的颜色、字体、阴影等配置值。
*   **6.2 图标引入:**
    *   选择并集成一个SVG图标库 (如 Heroicons, Lucide) 或使用 react-icons。确保按需加载。
*   **6.3 组件属性扩展:** 可能需要为现有基础组件增加新的 props 以支持更细致的样式控制 (如按钮变体、标签颜色等)。

**7. 验收标准 (V1.1)**

*   **7.1 视觉细节:**
    *   所有颜色、字体、间距、阴影等均符合V1.1规范中定义的优化值。
    *   图标已正确应用且风格统一。
    *   组件（按钮、标签等）不同状态下的视觉表现清晰、一致且符合预期。
*   **7.2 交互反馈:**
    *   所有交互元素的反馈（Hover, Active, Focus, Disabled）细腻且符合 Apple 风格的预期。
*   **7.3 (其他标准同V1.0)** 功能、性能、兼容性等。

**8. 里程碑与排期 (V1.1 迭代周期)**

*   **阶段一: 规范更新与资源准备 (X 天)**
    *   确认并更新设计规范文档 (颜色、字体、图标等细节)。
    *   准备所需图标资源。
*   **阶段二: 全局样式与基础组件更新 (X 天)**
    *   更新主题配置文件 (theme.ts / tailwind.config.js)。
    *   修改核心基础UI组件以符合新规范。
*   **阶段三: 页面细节调整与测试 (X 天/模块)**
    *   逐个页面应用优化后的样式和组件细节。
    *   进行视觉走查和功能回归测试。
*   **阶段四: 整体测试与发布**