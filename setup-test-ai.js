// 设置测试AI配置的脚本
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const DB_PATH = './server/ai_configs.db';

function setupTestAI() {
  console.log('🔧 设置测试AI配置...\n');

  const db = new sqlite3.Database(DB_PATH, (err) => {
    if (err) {
      console.error('❌ 数据库连接失败:', err.message);
      return;
    }
    console.log('✅ 连接到数据库');
  });

  // 插入一个测试AI配置
  const insertSQL = `
    INSERT OR REPLACE INTO ai_configs (
      id, name, provider, model_id, api_url, api_key_env_var, is_active
    ) VALUES (
      1, 'Test AI', 'test', 'test-model', 'http://localhost:3000/test', 'TEST_API_KEY', 1
    )
  `;

  db.run(insertSQL, function(err) {
    if (err) {
      console.error('❌ 插入AI配置失败:', err.message);
    } else {
      console.log('✅ 测试AI配置已添加');
    }

    // 查询确认
    db.all('SELECT * FROM ai_configs', [], (err, rows) => {
      if (err) {
        console.error('❌ 查询失败:', err.message);
      } else {
        console.log('📋 当前AI配置:');
        rows.forEach(row => {
          console.log(`  - ${row.name} (${row.provider}/${row.model_id}) - ${row.is_active ? '启用' : '禁用'}`);
        });
      }

      db.close((err) => {
        if (err) {
          console.error('❌ 关闭数据库失败:', err.message);
        } else {
          console.log('\n✅ 数据库连接已关闭');
        }
      });
    });
  });
}

setupTestAI();
