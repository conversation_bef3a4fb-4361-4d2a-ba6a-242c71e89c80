const express = require('express');
const router = express.Router();
const { runQuery, getQuery, allQuery } = require('../models/db');
const { clearAIConfigCache } = require('../services/aiService');
const { auth, adminAuth } = require('./authMiddleware');

/**
 * @route GET /api/configs/ai
 * @description 获取所有AI配置
 * @access Public
 */
router.get('/', auth, async (req, res) => {
  try {
    const configs = await allQuery('SELECT * FROM ai_configs');
    res.json(configs);
  } catch (err) {
    console.error('Error fetching AI configs:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

/**
 * @route POST /api/configs/ai
 * @description 创建新的AI配置
 * @access Public
 */
router.post('/', auth, adminAuth, async (req, res) => {
  const { name, provider, model_id, api_url, api_key_env_var, is_active } = req.body;
  if (!name || !provider || !model_id || !api_key_env_var) {
    return res.status(400).json({ message: 'Missing required fields' });
  }
  try {
    const result = await runQuery(
      'INSERT INTO ai_configs (name, provider, model_id, api_url, api_key_env_var, is_active) VALUES (?, ?, ?, ?, ?, ?)',
      [name, provider, model_id, api_url, api_key_env_var, is_active]
    );
    res.status(201).json({ id: result.id, message: 'AI config created successfully' });
    clearAIConfigCache();
  } catch (err) {
    console.error('Error creating AI config:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

/**
 * @route PUT /api/configs/ai/:id
 * @description 更新AI配置
 * @access Public
 */
router.put('/:id', auth, adminAuth, async (req, res) => {
  const { id } = req.params;
  const { name, provider, model_id, api_url, api_key_env_var, is_active } = req.body;
  if (!name || !provider || !model_id || !api_key_env_var) {
    return res.status(400).json({ message: 'Missing required fields' });
  }
  try {
    const result = await runQuery(
      'UPDATE ai_configs SET name = ?, provider = ?, model_id = ?, api_url = ?, api_key_env_var = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [name, provider, model_id, api_url, api_key_env_var, is_active, id]
    );
    if (result.changes === 0) {
      return res.status(404).json({ message: 'AI config not found' });
    }
    res.json({ message: 'AI config updated successfully' });
    clearAIConfigCache();
  } catch (err) {
    console.error('Error updating AI config:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

/**
 * @route DELETE /api/configs/ai/:id
 * @description 删除AI配置
 * @access Public
 */
router.delete('/:id', auth, adminAuth, async (req, res) => {
  const { id } = req.params;
  try {
    const result = await runQuery('DELETE FROM ai_configs WHERE id = ?', [id]);
    if (result.changes === 0) {
      return res.status(404).json({ message: 'AI config not found' });
    }
    res.json({ message: 'AI config deleted successfully' });
  } catch (err) {
    console.error('Error deleting AI config:', err.message);
    res.status(500).json({ message: 'Internal server error' });
  }
});

module.exports = router;