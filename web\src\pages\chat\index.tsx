import React, { useEffect, useState, useRef } from 'react';
import axios from 'axios';
import styled from 'styled-components';
import { v4 as uuidv4 } from 'uuid'; // For generating unique message IDs

// Import new components
import MessageBubble from '../../components/organisms/MessageBubble/MessageBubble';
import ChatInput from '../../components/organisms/ChatInput/ChatInput';
import PersonaSwitcher from '../../components/organisms/PersonaSwitcher/PersonaSwitcher';
import MessageList from '../../components/organisms/MessageList/MessageList';
import TypingIndicator from '../../components/animations/TypingIndicator/TypingIndicator';
import ChatLayout from '../../layouts/ChatLayout';
import { Typography } from '../../components/atoms';

// Update types to match new component props
interface Persona {
  id: string;
  name: string;
  avatar?: string;
  prompt?: string;
  description?: string;
  tags?: string[];
}

interface Message {
  id: string;
  sender: 'user' | 'ai';
  content: string;
  timestamp: Date;
  persona?: string;
}

interface NewChatResponse {
  id: string;
  personas: string[];
}

interface SendMessageResponse {
  sender: 'AI';
  content: string;
  timestamp: number;
}

const ChatPage: React.FC = () => {
  const [personas, setPersonas] = useState<Persona[]>([]);
  const [selectedPersona, setSelectedPersona] = useState<Persona | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isAiTyping, setIsAiTyping] = useState(false);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);

  // Fetch personas on mount
  useEffect(() => {
    const fetchPersonas = async () => {
      try {
        const res = await axios.get<Persona[]>('/api/persona');
        setPersonas(res.data);
        // Set a default selected persona if none is selected
        if (res.data.length > 0 && !selectedPersona) {
          setSelectedPersona(res.data[0]);
        }
      } catch (error) {
        console.error('Error fetching personas:', error);
      }
    };
    fetchPersonas();
  }, []);

  // Fetch chat history for the selected persona
  useEffect(() => {
    const fetchChatHistory = async () => {
      if (selectedPersona) {
        try {
          // In a real app, you might have a chat history API per persona or retrieve based on currentChatId
          // For now, let's simulate fetching history if a chat exists or start a new one.
          // This part needs adjustment based on your backend logic for chat sessions.
          const res = await axios.get<any>(`/api/chat/persona/${selectedPersona.id}`); // Assuming `any` for now as backend response structure is unknown
          if (res.data && res.data.messages) {
            setMessages(res.data.messages.map((m: any) => ({
              ...m,
              timestamp: new Date(m.timestamp),
              sender: m.sender === 'AI' ? 'ai' : 'user', // AI消息的sender是'AI'，用户消息的sender是人格ID
              persona: m.sender === 'AI' ? selectedPersona.name : undefined,
            })));
            setCurrentChatId(res.data.id);
          } else {
            setMessages([]);
            setCurrentChatId(null);
          }
        } catch (error) {
          console.error('Error fetching chat history:', error);
          setMessages([]);
          setCurrentChatId(null);
        }
      }
    };
    fetchChatHistory();
  }, [selectedPersona]);

  const handleSendMessage = async (content: string) => {
    if (!selectedPersona) {
      console.error('No persona selected.');
      return;
    }

    const userMessage: Message = {
      id: uuidv4(),
      content,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setIsAiTyping(true);

    try {
      let chatId = currentChatId;
      if (!chatId) {
        // Create a new chat if one doesn't exist for the selected persona
        const newChatRes = await axios.post<NewChatResponse>('/api/chat', { personas: [selectedPersona.id] });
        chatId = newChatRes.data.id;
        setCurrentChatId(chatId);
      }

      const response = await axios.post<SendMessageResponse>(`/api/chat/${chatId}/message`, {
        sender: selectedPersona.id, // 使用人格ID作为sender
        content,
        personaId: selectedPersona.id, // Pass persona ID to backend
        personaPrompt: selectedPersona.prompt, // Pass persona prompt to backend
        personaName: selectedPersona.name, // 傳遞人格名稱
        personaDescription: selectedPersona.description, // 傳遞人格描述
        personaTags: selectedPersona.tags, // 傳遞人格特徵
      });

      const aiMessage: Message = {
        id: uuidv4(),
        content: response.data.content,
        sender: 'ai',
        persona: selectedPersona.name,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, aiMessage]);
    } catch (error) {
      console.error('Failed to send message:', error);
      // Optionally add an error message bubble
    } finally {
      setIsAiTyping(false);
    }
  };

  return (
    <ChatLayout>
      <ChatHeader>
        <Typography variant="title2">對話</Typography>
        {personas.length > 0 && (
          <PersonaSwitcher
            personas={personas}
            selected={selectedPersona}
            onChange={setSelectedPersona}
          />
        )}
      </ChatHeader>
      <StyledMessageList>
        {messages.map((message) => (
          <MessageBubble key={message.id} message={message} />
        ))}
        {isAiTyping && <TypingIndicator />}
      </StyledMessageList>
      <ChatInput onSend={handleSendMessage} disabled={isAiTyping} />
    </ChatLayout>
  );
};

// Apple风格对话页面样式
const ChatHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.xl} ${({ theme }) => theme.spacing.pageSpacing};
  border-bottom: 1px solid ${({ theme }) => theme.colors.separator};
  background-color: ${({ theme }) => theme.colors.background};
  flex-shrink: 0;
  backdrop-filter: blur(20px); // Apple风格毛玻璃效果
  -webkit-backdrop-filter: blur(20px);

  h2 {
    margin: 0;
    color: ${({ theme }) => theme.colors.text};
  }
`;

const StyledMessageList = styled(MessageList)`
  flex-grow: 1;
  overflow-y: auto;
  padding: ${({ theme }) => theme.spacing.xl} ${({ theme }) => theme.spacing.pageSpacing};
  background-color: ${({ theme }) => theme.colors.background};

  /* 自定义滚动条 - Apple风格 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: ${({ theme }) => theme.colors.separator};
    border-radius: ${({ theme }) => theme.radius.round};

    &:hover {
      background-color: ${({ theme }) => theme.colors.textSecondary};
    }
  }

  /* PRD要求：聊天内容区域空状态设计 */
  &:empty::after {
    content: "選擇一個人格開始對話吧！";
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: ${({ theme }) => theme.colors.textSecondary};
    font-size: ${({ theme }) => theme.typography.callout.fontSize};
    text-align: center;
  }
`;

export default ChatPage; 