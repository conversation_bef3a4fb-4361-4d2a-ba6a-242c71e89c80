const jwt = require('jsonwebtoken');
const JWT_SECRET = process.env.JWT_SECRET || 'dev_secret';

function auth(req, res, next) {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token) return res.status(401).json({ message: '未授权' });
  try {
    req.user = jwt.verify(token, JWT_SECRET); // token 中应包含 account 和 role
    next();
  } catch {
    res.status(401).json({ message: 'Token无效' });
  }
}

/**
 * @function adminAuth
 * @description 验证用户是否为管理员的中间件。
 * @param {Object} req - 请求对象。
 * @param {Object} res - 响应对象。
 * @param {Function} next - 下一个中间件函数。
 */
function adminAuth(req, res, next) {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({ message: '无权限访问，需要管理员权限' })
  }
}

module.exports = { auth, adminAuth };