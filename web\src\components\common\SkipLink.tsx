import React from 'react';
import styled from 'styled-components';

interface SkipLinkProps {
  targetId: string; // The ID of the main content area to skip to
  children?: React.ReactNode;
}

const SkipLink: React.FC<SkipLinkProps> = ({ targetId, children = '跳至主要内容' }) => {
  return (
    <StyledSkipLink href={`#${targetId}`}>
      {children}
    </StyledSkipLink>
  );
};

const StyledSkipLink = styled.a`
  position: absolute;
  top: -999px;
  left: -999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
  z-index: 9999;

  &:focus {
    position: static;
    width: auto;
    height: auto;
    background-color: ${({ theme }) => theme.colors.primary};
    color: #FFFFFF;
    padding: ${({ theme }) => theme.spacing.sm};
    text-align: center;
    text-decoration: none;
  }
`;

export default SkipLink;