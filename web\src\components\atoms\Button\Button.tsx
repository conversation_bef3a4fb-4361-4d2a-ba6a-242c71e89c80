import React, { ReactNode, ButtonHTMLAttributes } from 'react';
import styled, { css } from 'styled-components';


interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'text';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  children: ReactNode;
  onClick?: () => void;
}

const StyledButton = styled.button<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${(props) => props.theme.spacing.sm};
  font-family: ${(props) => props.theme.typography.callout.fontFamily};
  font-weight: 500;
  line-height: ${(props) => props.theme.typography.callout.lineHeight};
  letter-spacing: ${(props) => props.theme.typography.callout.letterSpacing};
  text-decoration: none;
  white-space: nowrap;
  border-radius: ${(props) => props.theme.radius.sm};
  cursor: pointer;
  user-select: none;
  transition: all 0.15s ease-out;
  position: relative;
  overflow: hidden;
  border: none;

  /* 尺寸样式 */
  ${(props) => {
    switch (props.size) {
      case 'sm':
        return css`
          font-size: ${props.theme.typography.subhead.fontSize};
          font-weight: 500;
          line-height: ${props.theme.typography.subhead.lineHeight};
          padding: ${props.theme.spacing.sm} ${props.theme.spacing.lg};
          min-height: 32px;
          border-radius: ${props.theme.radius.sm};
        `;
      case 'lg':
        return css`
          font-size: ${props.theme.typography.callout.fontSize};
          font-weight: 500;
          line-height: ${props.theme.typography.callout.lineHeight};
          padding: ${props.theme.spacing.lg} ${props.theme.spacing.xxl};
          min-height: 48px;
          border-radius: ${props.theme.radius.md};
        `;
      default: // md
        return css`
          font-size: ${props.theme.typography.callout.fontSize};
          font-weight: 500;
          line-height: ${props.theme.typography.callout.lineHeight};
          padding: ${props.theme.spacing.md} ${props.theme.spacing.xl};
          min-height: 40px;
          border-radius: ${props.theme.radius.sm};
        `;
    }
  }}

  /* 变体样式 */
  ${(props) => {
    switch (props.variant) {
      case 'secondary':
        return css`
          background-color: #E8E8ED; /* PRD推荐：浅灰色背景 */
          color: ${props.theme.colors.text}; /* 深灰色文字 */
          border: none;

          &:hover:not(:disabled) {
            background-color: #DCDCE0; /* 悬浮时稍深 */
            transform: translateY(-1px);
            box-shadow: ${props.theme.shadows.xs};
          }

          &:active:not(:disabled) {
            background-color: #D0D0D4; /* 按下时更深 */
            transform: translateY(0px);
          }
        `;
      case 'ghost':
        return css`
          background-color: transparent;
          color: ${props.theme.colors.primary};
          border: 1px solid ${props.theme.colors.primary};

          &:hover:not(:disabled) {
            background-color: ${props.theme.colors.primary};
            color: ${props.theme.colors.surface};
            transform: translateY(-1px);
          }

          &:active:not(:disabled) {
            transform: translateY(0px);
          }
        `;
      case 'text':
        return css`
          background-color: transparent;
          color: ${props.theme.colors.primary};
          border: none;
          padding: ${props.theme.spacing.sm} ${props.theme.spacing.md};

          &:hover:not(:disabled) {
            background-color: ${props.theme.colors.surfaceSecondary};
            transform: translateY(-1px);
          }

          &:active:not(:disabled) {
            transform: translateY(0px);
          }
        `;
      default: // primary
        return css`
          background-color: ${props.theme.colors.primary};
          color: ${props.theme.colors.surface};
          border: none;

          &:hover:not(:disabled) {
            background-color: ${props.theme.colors.primary};
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: ${props.theme.shadows.sm};
          }

          &:active:not(:disabled) {
            opacity: 0.8;
            transform: translateY(0px);
          }
        `;
    }
  }}

  /* 禁用状态 */
  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    pointer-events: none;
    transform: none !important;
  }

  /* 加载状态 */
  ${({ loading }) => loading && css`
    color: transparent;
    pointer-events: none;
  `}

  /* 焦点样式 */
  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
    border-radius: ${({ theme }) => theme.radius.sm};
  }
`;

// 简单的加载指示器
const LoadingText = styled.span`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: inherit;
`;

export const Button = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  children,
  onClick,
  type = 'button',
  ...props
}: ButtonProps) => {

  return (
    <StyledButton
      variant={variant}
      size={size}
      loading={loading}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
      aria-disabled={disabled || loading}
      aria-busy={loading}
      {...props}
    >
      <span style={{ opacity: loading ? 0 : 1 }}>
        {children}
      </span>

      {loading && <LoadingText>...</LoadingText>}
    </StyledButton>
  );
};