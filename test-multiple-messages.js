// 测试多条消息的脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testMultipleMessages() {
  console.log('🧪 开始测试多条消息...\n');

  try {
    // 1. 创建新对话
    console.log('💬 创建新对话...');
    const chatResponse = await axios.post(`${BASE_URL}/api/chat`, {
      personas: [1]
    });
    const chatId = chatResponse.data.id;
    console.log('✅ 创建对话成功，ID:', chatId);
    
    // 2. 发送多条消息
    const messages = [
      '你好，我是用户',
      '今天天气很好',
      '我感觉很开心',
      '你觉得呢？',
      '我们聊聊其他话题吧'
    ];
    
    for (let i = 0; i < messages.length; i++) {
      console.log(`\n📝 发送第${i + 1}条消息: "${messages[i]}"`);
      const messageResponse = await axios.post(`${BASE_URL}/api/chat/${chatId}/message`, {
        sender: 1,
        content: messages[i],
        personaPrompt: '你是一个友好聪明的助手',
        personaName: '测试人格',
        personaDescription: '这是一个测试人格',
        personaTags: ['友好', '聪明']
      });
      console.log('✅ AI回复:', messageResponse.data.content);
      
      // 稍微延迟一下
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 3. 检查成长档案数据
    console.log('\n📊 检查成长档案数据...');
    const archiveResponse = await axios.get(`${BASE_URL}/api/archive/chats`);
    console.log(`✅ 对话记录数量: ${archiveResponse.data.length}`);
    
    const reportResponse = await axios.get(`${BASE_URL}/api/archive/report`);
    console.log('✅ 成长报告:');
    console.log(`  - 总对话数: ${reportResponse.data.totalConversations}`);
    console.log(`  - 活跃人格: ${reportResponse.data.activePersonas}`);
    console.log(`  - 成长指数: ${reportResponse.data.growthIndex}`);
    console.log(`  - 今日消息数: ${reportResponse.data.dailyActivity[6].value}`);
    
    console.log('\n🎉 多条消息测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testMultipleMessages();
