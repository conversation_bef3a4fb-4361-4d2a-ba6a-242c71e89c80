import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { Typography } from '../../atoms';
import { format } from 'date-fns';

interface TimelineItem {
  id: string;
  date: Date;
  persona: string;
  summary: string;
  mood: 'positive' | 'neutral' | 'negative';
  duration: number;
}

interface ConversationTimelineProps {
  items: TimelineItem[];
}

const ConversationTimeline: React.FC<ConversationTimelineProps> = ({ items }) => {
  return (
    <TimelineContainer>
      {items.map((item, index) => (
        <TimelineEntry
          key={item.id}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <TimelineNode mood={item.mood} />
          <TimelineContent>
            <Typography variant="caption1" color="secondary">
              {format(item.date, 'yyyy/MM/dd HH:mm')}
            </Typography>
            <Typography variant="title3">{item.persona}</Typography>
            <Typography variant="body">{item.summary}</Typography>
            <Typography variant="caption1">
              对话时长: {item.duration}分钟
            </Typography>
          </TimelineContent>
        </TimelineEntry>
      ))}
    </TimelineContainer>
  );
};

const TimelineContainer = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  padding-left: 20px; /* Space for the timeline nodes */
  &::before {
    content: '';
    position: absolute;
    left: 8px; /* Align with node centers */
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: ${({ theme }) => theme.colors.separator};
  }
`;

const TimelineEntry = styled(motion.div)`
  display: flex;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const TimelineNode = styled.div<{ mood: TimelineItem['mood'] }>`
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: ${({ theme, mood }) => {
    switch (mood) {
      case 'positive':
        return theme.colors.green[500];
      case 'negative':
        return theme.colors.red[500];
      case 'neutral':
      default:
        return theme.colors.gray[500];
    }
  }};
  border: 2px solid ${({ theme }) => theme.colors.background};
  flex-shrink: 0;
  margin-left: -8px; /* Pull into the line */
  margin-right: ${({ theme }) => theme.spacing.md};
  z-index: 1;
`;

const TimelineContent = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  background-color: ${({ theme }) => theme.colors.surface};
  padding: ${({ theme }) => theme.spacing.md};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

export default ConversationTimeline; 