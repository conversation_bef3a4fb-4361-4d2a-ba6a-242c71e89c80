import React from 'react';
import styled from 'styled-components';
import { useTheme } from '../design-system/theme/ThemeProvider';
import { Icon, Typography, Button } from '../components/atoms';
import { NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { toggleTheme } = useTheme();
  const { user, logout, isAdmin } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/auth/login');
  };

  return (
    <LayoutContainer>
      <StyledNavigation>
        <NavSection>
          <NavLink to="/persona">
            <Icon name="persona" size="24px" />
            <Typography variant="caption1">人格管理</Typography>
          </NavLink>
          <NavLink to="/chat">
            <Icon name="chat" size="24px" />
            <Typography variant="caption1">对话</Typography>
          </NavLink>
          <NavLink to="/archive">
            <Icon name="archive" size="24px" />
            <Typography variant="caption1">成长档案</Typography>
          </NavLink>

          {/* 管理员专用菜单 */}
          {isAdmin() && (
            <AdminSection>
              <SectionDivider />
              <SectionTitle>
                <Typography variant="caption2" color="textSecondary">管理员</Typography>
              </SectionTitle>
              <NavLink to="/admin/aiconfig">
                <Icon name="settings" size="24px" />
                <Typography variant="caption1">大模型配置</Typography>
              </NavLink>
            </AdminSection>
          )}
        </NavSection>

        <BottomSection>
          {/* PRD要求：重新设计的用户信息区域 */}
          <UserInfo>
            <UserAvatar>
              {user?.account?.charAt(0).toUpperCase() || 'U'}
            </UserAvatar>
            <UserDetails>
              <UserName>{user?.account || '用户'}</UserName>
              <UserRole>{user?.role === 'admin' ? '管理员' : '用户'}</UserRole>
            </UserDetails>
          </UserInfo>

          <ActionButtons>
            {/* PRD要求：主题切换使用明确图标 */}
            <ThemeToggle onClick={toggleTheme} title="切换主题">
              <Icon name="sun" size="18px" />
            </ThemeToggle>
            <LogoutButton onClick={handleLogout} title="登出">
              <Icon name="logout" size="18px" />
            </LogoutButton>
          </ActionButtons>
        </BottomSection>
      </StyledNavigation>
      <MainContent>{children}</MainContent>
    </LayoutContainer>
  );
};

const LayoutContainer = styled.div`
  display: flex;
  height: 100vh;
`;

// Apple风格侧边栏 - PRD要求优化背景色、间距等
const StyledNavigation = styled.nav`
  flex-shrink: 0;
  width: 260px; // 稍微增加宽度，提供更好的呼吸感
  background-color: ${({ theme }) => theme.colors.surfaceSecondary}; // PRD要求：稍深于主内容区的灰色
  border-right: none; // Apple风格通常无边框，依靠颜色区分
  padding: ${({ theme }) => theme.spacing.xl} ${({ theme }) => theme.spacing.lg};
  display: flex;
  flex-direction: column;
  height: 100vh;
  /* 可选：毛玻璃效果（需要技术评估）
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  */
`;

// Apple风格导航菜单 - PRD要求增加垂直间距，提供呼吸感
const NavSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs}; // 菜单项之间的小间距

  a {
    display: flex;
    align-items: center;
    gap: ${({ theme }) => theme.spacing.md}; // 图标和文字间距
    padding: ${({ theme }) => theme.spacing.md} ${({ theme }) => theme.spacing.lg}; // PRD要求增加垂直间距
    border-radius: ${({ theme }) => theme.radius.md}; // Apple风格圆角
    color: ${({ theme }) => theme.colors.textSecondary};
    text-decoration: none;
    transition: all 0.15s ease-out; // Apple风格更快的过渡
    font-weight: ${({ theme }) => theme.typography.callout.fontWeight};
    font-size: ${({ theme }) => theme.typography.callout.fontSize};
    min-height: 44px; // Apple推荐的最小触摸目标

    /* PRD要求：菜单项选中状态 - 更柔和的背景色 */
    &.active {
      background-color: ${({ theme }) => `${theme.colors.primary}15`}; /* 主蓝色15%透明度 */
      color: ${({ theme }) => theme.colors.primary}; /* 主蓝色文字 */
      font-weight: 500; // Medium字重
      box-shadow: ${({ theme }) => theme.shadows.xs};
      border: 1px solid ${({ theme }) => `${theme.colors.primary}25`}; /* 主蓝色25%透明度边框 */
    }

    &:hover:not(.active) {
      background-color: ${({ theme }) => theme.colors.separator};
      color: ${({ theme }) => theme.colors.text};
      transform: translateX(2px); // 轻微的悬浮效果
    }

    /* 图标样式统一 */
    svg {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
    }
  }
`;

// PRD要求：菜单项分组之间使用细微分割线或小标题区分
const AdminSection = styled.div`
  margin-top: ${({ theme }) => theme.spacing.xxl}; // 增加分组间距
`;

const SectionDivider = styled.hr`
  border: none;
  height: 1px;
  background-color: ${({ theme }) => theme.colors.separator};
  margin: ${({ theme }) => theme.spacing.xl} 0;
  opacity: 0.6; // 更细微的分割线
`;

const SectionTitle = styled.div`
  padding: 0 ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.md};

  /* 小标题样式 */
  p {
    font-size: ${({ theme }) => theme.typography.caption1.fontSize};
    font-weight: 500; // Medium字重
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
  }
`;

// PRD要求：侧边栏底部用户信息区域重新设计
const BottomSection = styled.div`
  margin-top: auto;
  padding-top: ${({ theme }) => theme.spacing.xl};
  border-top: 1px solid ${({ theme }) => theme.colors.separator};
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.radius.lg};
  box-shadow: ${({ theme }) => theme.shadows.xs};
`;

// PRD要求：圆形用户头像占位符
const UserAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: ${({ theme }) => theme.radius.round};
  background-color: ${({ theme }) => theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.surface};
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
`;

const UserDetails = styled.div`
  flex: 1;
  min-width: 0; // 防止文字溢出
`;

const UserName = styled.div`
  font-size: ${({ theme }) => theme.typography.callout.fontSize};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const UserRole = styled.div`
  font-size: ${({ theme }) => theme.typography.caption1.fontSize};
  color: ${({ theme }) => theme.colors.textSecondary};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  justify-content: center;
`;

// PRD要求：主题切换使用明确图标和交互反馈
const ThemeToggle = styled.button`
  padding: ${({ theme }) => theme.spacing.md};
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.radius.round}; // 圆形按钮
  background-color: ${({ theme }) => theme.colors.surface};
  border: none;
  color: ${({ theme }) => theme.colors.textSecondary};
  cursor: pointer;
  transition: all 0.15s ease-out;
  width: 40px;
  height: 40px;
  box-shadow: ${({ theme }) => theme.shadows.xs};

  &:hover {
    background-color: ${({ theme }) => theme.colors.separator};
    color: ${({ theme }) => theme.colors.text};
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }

  &:active {
    transform: translateY(0px);
  }

  svg {
    width: 18px;
    height: 18px;
  }
`;

const LogoutButton = styled.button`
  padding: ${({ theme }) => theme.spacing.md};
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${({ theme }) => theme.radius.round}; // 圆形按钮
  background-color: ${({ theme }) => theme.colors.surface};
  border: none;
  color: ${({ theme }) => theme.colors.red};
  cursor: pointer;
  transition: all 0.15s ease-out;
  width: 40px;
  height: 40px;
  box-shadow: ${({ theme }) => theme.shadows.xs};

  &:hover {
    background-color: ${({ theme }) => theme.colors.red};
    color: ${({ theme }) => theme.colors.surface};
    transform: translateY(-1px);
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }

  &:active {
    transform: translateY(0px);
  }

  svg {
    width: 18px;
    height: 18px;
  }
`;

const MainContent = styled.main`
  flex-grow: 1;
  overflow-y: auto;
`;

export default AppLayout;