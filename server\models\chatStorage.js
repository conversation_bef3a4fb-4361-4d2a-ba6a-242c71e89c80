// 共享的聊天数据存储
let chats = [];
let nextChatId = 1;

// 获取所有聊天记录
function getAllChats() {
  return chats;
}

// 根据ID获取聊天记录
function getChatById(id) {
  return chats.find(chat => chat.id === parseInt(id));
}

// 创建新的聊天记录
function createChat(personas) {
  const newChat = {
    id: nextChatId++,
    personas: personas || [],
    messages: []
  };
  chats.push(newChat);
  return newChat;
}

// 添加消息到聊天记录
function addMessageToChat(chatId, message) {
  const chat = getChatById(chatId);
  if (chat) {
    chat.messages.push(message);
    return message;
  }
  return null;
}

// 清空所有聊天记录（用于测试）
function clearAllChats() {
  chats = [];
  nextChatId = 1;
}

module.exports = {
  getAllChats,
  getChatById,
  createChat,
  addMessageToChat,
  clearAllChats
};
