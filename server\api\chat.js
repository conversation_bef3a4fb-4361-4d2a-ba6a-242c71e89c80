const express = require('express');
const router = express.Router();
const { generateAIResponse } = require('../services/aiService'); // 引入 aiService

let chats = [];
let chatIdCounter = 1;

// 聊天數據結構: { id, personas: [人格id], messages: [{ sender, content, timestamp }] }

// 創建新對話（單/多人人格）
router.post('/', (req, res) => {
  const { personas } = req.body; // personas: [人格id]
  if (!Array.isArray(personas) || personas.length === 0) return res.status(400).json({ message: '請選擇至少一個人格' });
  const chat = { id: chatIdCounter++, personas, messages: [] };
  chats.push(chat);
  res.json(chat);
});

// 查詢所有對話
router.get('/', (req, res) => {
  res.json(chats);
});

// 發送消息（單/多人人格）
router.post('/:chatId/message', async (req, res) => {
  console.log('req.body+++++++++++', req.body);
  const chatId = parseInt(req.params.chatId);
  const { sender, content, personaPrompt, personaName, personaDescription, personaTags } = req.body; // 獲取更多人格資訊
  const chat = chats.find(c => c.id === chatId);
  if (!chat) return res.status(404).json({ message: '對話不存在' });
  if (!sender || !content) return res.status(400).json({ message: '發言人和內容必填' });

  const userMsg = { sender, content, timestamp: Date.now() };
  chat.messages.push(userMsg);

  try {
    // 構建更豐富且優化的系統提示
    let systemPrompt = '你是一個語言模型，現在扮演‘我’的另外一個人格。請以‘我’的一個另外人格的口吻回答問題。'; // 基礎提示

    if (personaName) systemPrompt += `\n你的名字是 ${personaName}。`;
    if (personaDescription) systemPrompt += `\n你的描述是：「${personaDescription}」。`;
    if (personaTags && personaTags.length > 0) systemPrompt += `\n你的特徵包括：${personaTags.join('、')}。`; // 使用頓號作為分隔符

    if (personaPrompt) {
      systemPrompt += `\n請遵循以下指令：${personaPrompt}`; // 將 personaPrompt 作為明確指令
    } else {
      systemPrompt += `\n請盡量簡潔的回答問題。`; // 如果沒有 personaPrompt，則提供預設回答風格
    }
    console.log('systemPrompt+++++++++++', systemPrompt);

    // 直接調用 aiService 中的函數，使用豐富的系統提示
    const aiMessageContent = await generateAIResponse(systemPrompt, content);
    // console.log('aiMessageContent+++++++++++', aiMessageContent);
    const aiMsg = { sender: 'AI', content: aiMessageContent, timestamp: Date.now() };
    chat.messages.push(aiMsg);

    res.json(aiMsg);
  } catch (error) {
    console.error('AI 生成失敗:', error.message);
    res.status(500).json({ message: 'AI 生成失敗', detail: error.message });
  }
});

// 查詢單個對話詳情
router.get('/:chatId', (req, res) => {
  const chatId = parseInt(req.params.chatId);
  const chat = chats.find(c => c.id === chatId);
  if (!chat) return res.status(404).json({ message: '對話不存在' });
  res.json(chat);
});

// 根據 persona ID 查詢聊天歷史
router.get('/persona/:personaId', (req, res) => {
  const personaId = parseInt(req.params.personaId); // 將 personaId 轉換為數字
  const chat = chats.find(c => c.personas.includes(personaId)); // 查找包含該 personaId 的第一個聊天
  if (!chat) {
    return res.status(404).json({ message: '該人格無對話歷史' });
  }
  res.json({ id: chat.id, messages: chat.messages });
});

module.exports = router;
module.exports.chats = chats;